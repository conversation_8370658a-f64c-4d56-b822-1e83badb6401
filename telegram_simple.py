"""
Simple Telegram Bot using direct API calls (Python 3.13 compatible)
Enhanced with error handling and health monitoring
"""
import asyncio
import aiohttp
import json
import ssl
import certifi
from decimal import Decimal
from typing import Dict, List, Optional
from datetime import datetime, timedelta
# Import directly from root config.py
import importlib.util
spec = importlib.util.spec_from_file_location("root_config", "./config.py")
root_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(root_config)
from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer
# Temporarily disable monitoring imports
# from core.monitoring.error_handler import error_handler
# from core.monitoring.health_monitor import health_monitor
from loguru import logger

class SimpleTelegramBot:
    """Simple Telegram bot using direct API calls"""

    def __init__(self):
        self.settings = root_config.get_settings()
        self.bot_token = self.settings.telegram_bot_token
        self.admin_user_id = self.settings.telegram_admin_user_ids
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"

        self.exchange_manager = None
        self.strategy_manager = None
        self.market_analyzer = None
        self.session = None
        self.running = False

        # User input tracking
        self.user_input_state = {}  # Track what input we're expecting from users

        # PERFORMANCE OPTIMIZATIONS
        self._menu_cache = {}  # Cache frequently used menus
        self._last_balance_fetch = 0  # Cache balance fetches
        self._balance_cache = None
        self._processing_callbacks = set()  # Prevent duplicate processing
        self._callback_timeout = 5  # Seconds before callback times out

        # Set up error handler with telegram bot reference
        # error_handler.set_telegram_bot(self)

        # Initialize health monitoring
        # health_monitor.metrics['system_status'] = 'initializing'

    async def send_message(self, chat_id: int, text: str, parse_mode: str = "Markdown", reply_markup=None):
        """Send message to Telegram"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": parse_mode
            }

            if reply_markup:
                data["reply_markup"] = json.dumps(reply_markup)

            async with self.session.post(url, json=data) as response:
                return await response.json()

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return None

    async def edit_message(self, chat_id: int, message_id: int, text: str, parse_mode: str = "Markdown", reply_markup=None):
        """Edit existing message"""
        try:
            url = f"{self.base_url}/editMessageText"
            data = {
                "chat_id": chat_id,
                "message_id": message_id,
                "text": text,
                "parse_mode": parse_mode
            }

            if reply_markup:
                data["reply_markup"] = json.dumps(reply_markup)

            async with self.session.post(url, json=data) as response:
                return await response.json()

        except Exception as e:
            logger.error(f"Error editing message: {e}")
            return None

    async def answer_callback_query(self, callback_query_id: str, text: str = ""):
        """Answer callback query"""
        try:
            url = f"{self.base_url}/answerCallbackQuery"
            data = {
                "callback_query_id": callback_query_id,
                "text": text
            }

            async with self.session.post(url, json=data) as response:
                return await response.json()

        except Exception as e:
            logger.error(f"Error answering callback: {e}")
            return None

    async def get_updates(self, offset: int = 0, timeout: int = 30):
        """Get updates from Telegram"""
        try:
            url = f"{self.base_url}/getUpdates"
            params = {"offset": offset, "timeout": timeout}

            # Use aiohttp timeout
            http_timeout = aiohttp.ClientTimeout(total=timeout + 10)

            async with self.session.get(url, params=params, timeout=http_timeout) as response:
                return await response.json()

        except asyncio.TimeoutError:
            logger.debug(f"Timeout getting updates (offset: {offset})")
            return {"ok": True, "result": []}
        except aiohttp.ServerTimeoutError:
            logger.debug(f"Server timeout getting updates (offset: {offset})")
            return {"ok": True, "result": []}
        except Exception as e:
            logger.error(f"Error getting updates: {e}")
            return None

    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized - now open for everyone"""
        return True  # Allow everyone to use the bot

    def get_main_keyboard(self):
        """Get reorganized main menu keyboard with logical hierarchy"""
        return {
            "inline_keyboard": [
                [
                    {"text": "🚀 START TRADING", "callback_data": "primary_trading"}
                ],
                [
                    {"text": "💰 Portfolio", "callback_data": "menu_portfolio"},
                    {"text": "📊 Market Analysis", "callback_data": "menu_analysis"}
                ],
                [
                    {"text": "📈 Live Prices", "callback_data": "menu_market"},
                    {"text": "🤖 Trading Controls", "callback_data": "menu_trading"}
                ],
                [
                    {"text": "⚙️ Settings", "callback_data": "menu_settings"},
                    {"text": "❓ Help & Support", "callback_data": "menu_help"}
                ]
            ]
        }

    def get_portfolio_keyboard(self):
        """Get portfolio menu keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "💰 Balances", "callback_data": "portfolio_balance"},
                    {"text": "📊 Posities", "callback_data": "portfolio_positions"}
                ],
                [
                    {"text": "📈 Performance", "callback_data": "portfolio_performance"},
                    {"text": "💸 P&L", "callback_data": "portfolio_pnl"}
                ],
                [
                    {"text": "🔄 Convert Coins", "callback_data": "portfolio_convert"},
                    {"text": "⚡ Spot→Futures", "callback_data": "portfolio_futures"}
                ],
                [
                    {"text": "🔙 Terug", "callback_data": "back_main"}
                ]
            ]
        }

    def get_market_keyboard(self):
        """Get market menu keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "₿ BTC/USDT", "callback_data": "price_BTC/USDT"},
                    {"text": "Ξ ETH/USDT", "callback_data": "price_ETH/USDT"}
                ],
                [
                    {"text": "🟡 BNB/USDT", "callback_data": "price_BNB/USDT"},
                    {"text": "🔵 ADA/USDT", "callback_data": "price_ADA/USDT"}
                ],
                [
                    {"text": "🟣 SOL/USDT", "callback_data": "price_SOL/USDT"},
                    {"text": "🔍 Andere", "callback_data": "price_custom"}
                ],
                [
                    {"text": "📊 Beste Prijzen", "callback_data": "market_best_prices"},
                    {"text": "📈 Analyse", "callback_data": "market_analysis"}
                ],
                [
                    {"text": "🔙 Terug", "callback_data": "back_main"}
                ]
            ]
        }

    def get_primary_trading_keyboard(self):
        """Get primary trading interface - main entry point for all trading"""
        return {
            "inline_keyboard": [
                [
                    {"text": "🎯 Select Strategy", "callback_data": "select_strategy"}
                ],
                [
                    {"text": "💰 Set Amount", "callback_data": "set_trading_amount"},
                    {"text": "🚀 Quick Start", "callback_data": "quick_start_trading"}
                ],
                [
                    {"text": "📊 View Positions", "callback_data": "trading_positions"},
                    {"text": "🛑 Stop All Trading", "callback_data": "trading_stop_all"}
                ],
                [
                    {"text": "🔙 Back to Main", "callback_data": "back_main"}
                ]
            ]
        }

    def get_strategy_selection_keyboard(self):
        """Get strategy selection keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "📈 Day Trading", "callback_data": "strategy_daytrading"},
                    {"text": "⚡ Scalping", "callback_data": "strategy_scalping"}
                ],
                [
                    {"text": "🚀 Momentum", "callback_data": "strategy_momentum"},
                    {"text": "📊 Mean Reversion", "callback_data": "strategy_meanreversion"}
                ],
                [
                    {"text": "🤖 Auto (AI Choice)", "callback_data": "strategy_auto"},
                    {"text": "🔄 All Strategies", "callback_data": "strategy_all"}
                ],
                [
                    {"text": "🔙 Back", "callback_data": "primary_trading"}
                ]
            ]
        }

    def get_amount_selection_keyboard(self):
        """Get amount selection keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "💵 $10", "callback_data": "amount_10"},
                    {"text": "💵 $50", "callback_data": "amount_50"}
                ],
                [
                    {"text": "💵 $100", "callback_data": "amount_100"},
                    {"text": "💵 $250", "callback_data": "amount_250"}
                ],
                [
                    {"text": "💵 $500", "callback_data": "amount_500"},
                    {"text": "💵 $1000", "callback_data": "amount_1000"}
                ],
                [
                    {"text": "✏️ Custom Amount", "callback_data": "amount_custom"},
                    {"text": "💰 Use 25% Balance", "callback_data": "amount_percentage"}
                ],
                [
                    {"text": "🔙 Back", "callback_data": "primary_trading"}
                ]
            ]
        }

    def get_trading_keyboard(self):
        """Get advanced trading controls menu"""
        return {
            "inline_keyboard": [
                [
                    {"text": "🚀 Start Auto Trading", "callback_data": "trading_start"},
                    {"text": "🛑 Stop Auto Trading", "callback_data": "trading_stop"}
                ],
                [
                    {"text": "💱 Manual Trade", "callback_data": "trading_manual"},
                    {"text": "⚡ Force Trade", "callback_data": "trading_force"}
                ],
                [
                    {"text": "🤖 Strategy Settings", "callback_data": "trading_strategies"},
                    {"text": "📊 Active Positions", "callback_data": "trading_positions"}
                ],
                [
                    {"text": "🔄 Restart Trading", "callback_data": "trading_restart"},
                    {"text": "⚙️ Advanced Settings", "callback_data": "trading_advanced"}
                ],
                [
                    {"text": "🔙 Back to Main", "callback_data": "back_main"}
                ]
            ]
        }

    def get_analysis_keyboard(self):
        """Get analysis menu keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "📊 Live Analyse", "callback_data": "analysis_live"},
                    {"text": "🚨 Alerts", "callback_data": "analysis_alerts"}
                ],
                [
                    {"text": "📈 Technisch", "callback_data": "analysis_technical"},
                    {"text": "🤖 AI Analyse", "callback_data": "analysis_ai"}
                ],
                [
                    {"text": "🔄 Ververs", "callback_data": "analysis_refresh"},
                    {"text": "⏰ 5min Update", "callback_data": "analysis_auto"}
                ],
                [
                    {"text": "🔙 Terug", "callback_data": "back_main"}
                ]
            ]
        }

    def get_back_keyboard(self, back_to: str = "back_main"):
        """Get simple back keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "🔙 Terug", "callback_data": back_to}
                ]
            ]
        }

    def get_permanent_keyboard(self):
        """Get permanent keyboard that stays at the bottom"""
        return {
            "keyboard": [
                [
                    {"text": "💰 Portfolio"},
                    {"text": "📊 Markt"},
                    {"text": "🤖 Trading"}
                ],
                [
                    {"text": "📈 Analyse"},
                    {"text": "⚙️ Instellingen"},
                    {"text": "❓ Help"}
                ],
                [
                    {"text": "🚀 Start Trading"},
                    {"text": "🛑 Stop Trading"}
                ]
            ],
            "resize_keyboard": True,
            "one_time_keyboard": False,
            "persistent": True
        }

    def get_compact_permanent_keyboard(self):
        """Get reorganized compact permanent keyboard with logical hierarchy"""
        return {
            "keyboard": [
                [
                    {"text": "🚀 START TRADING"},
                    {"text": "💰 Portfolio"}
                ],
                [
                    {"text": "📊 Market Analysis"},
                    {"text": "📈 Live Prices"}
                ],
                [
                    {"text": "🤖 Trading Controls"},
                    {"text": "❓ Help"}
                ]
            ],
            "resize_keyboard": True,
            "one_time_keyboard": False,
            "persistent": True
        }

    async def handle_start(self, chat_id: int, user_id: int):
        """Handle /start command"""
        # Log new user
        logger.info(f"👋 New user started bot: {user_id}")

        welcome_message = """
🤖 **Welkom bij de AI Trading Bot!**

🌟 **Voor Iedereen Toegankelijk!**

✅ **KuCoin & MEXC** verbonden
🤖 **4 AI Strategieën** actief
📊 **Live Marktanalyse** elke 5 min
💰 **Real-time Portfolio** tracking

**Gebruik de toetsen onderaan voor snelle toegang!**
**Of kies een optie hieronder voor meer details:**

🎓 **Leer crypto trading** met real-time data en AI-analyse!
⚠️ **Let op:** Dit is een demo bot voor educatieve doeleinden.
        """

        # Send welcome message with both permanent keyboard and inline menu
        await self.send_message(chat_id, welcome_message, reply_markup=self.get_compact_permanent_keyboard())

        # Also send the detailed menu as inline keyboard
        menu_message = "📋 **Gedetailleerd Menu:**"
        await self.send_message(chat_id, menu_message, reply_markup=self.get_main_keyboard())

    async def handle_price(self, chat_id: int, user_id: int, args: list):
        """Handle /price command"""

        if not args:
            await self.send_message(chat_id, "❌ Gebruik: /price <symbol>\nVoorbeeld: /price BTC/USDT")
            return

        symbol = args[0].upper()
        await self.send_message(chat_id, f"📊 Prijzen ophalen voor {symbol}...")

        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                await self.send_message(chat_id, f"❌ Geen prijsdata gevonden voor {symbol}")
                return

            message = f"📊 **Prijzen voor {symbol}**\n\n"

            for exchange_name, ticker in tickers.items():
                message += f"**{exchange_name.upper()}:**\n"
                message += f"  Last: ${ticker.last:.8f}\n"
                message += f"  Bid: ${ticker.bid:.8f}\n"
                message += f"  Ask: ${ticker.ask:.8f}\n"
                message += f"  24h High: ${ticker.high:.8f}\n"
                message += f"  24h Low: ${ticker.low:.8f}\n\n"

            await self.send_message(chat_id, message)

        except Exception as e:
            logger.error(f"Error fetching price: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen prijs: {str(e)}")

    async def handle_balance(self, chat_id: int, user_id: int):
        """Handle /balance command - OPTIMIZED WITH CACHING"""

        await self.send_message(chat_id, "💰 Balances ophalen...")

        try:
            # Use cached balance if recent (within 30 seconds)
            import time
            current_time = time.time()
            if (self._balance_cache and
                current_time - self._last_balance_fetch < 30):
                all_balances = self._balance_cache
            else:
                all_balances = await self.exchange_manager.get_all_balances()
                self._balance_cache = all_balances
                self._last_balance_fetch = current_time

            message = "💰 **Portfolio Balances**\n\n"

            for exchange_name, balances in all_balances.items():
                message += f"**{exchange_name.upper()}:**\n"

                if not balances:
                    message += "  ⚠️ Geen balances gevonden\n"
                    message += "  💡 Mogelijke oorzaken:\n"
                    message += "    • API keys hebben geen read permissies\n"
                    message += "    • Sandbox mode zonder test balances\n"
                    message += "    • Verbindingsprobleem\n\n"
                    continue

                non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}

                if not non_zero_balances:
                    message += "  📊 Alle balances zijn 0\n"
                    message += "  💡 Dit is normaal in sandbox mode\n\n"
                    continue

                for currency, balance in sorted(non_zero_balances.items()):
                    message += f"  {currency}: {balance.total:.8f}\n"
                    if balance.free > 0:
                        message += f"    └ Beschikbaar: {balance.free:.8f}\n"
                    if balance.used > 0:
                        message += f"    └ In gebruik: {balance.used:.8f}\n"

                message += "\n"

            # Add debug info
            message += "🔧 **Debug Info:**\n"
            message += f"• KuCoin Sandbox: {'Ja' if self.settings.kucoin_sandbox else 'Nee'}\n"
            message += f"• MEXC Sandbox: {'Ja' if self.settings.mexc_sandbox else 'Nee'}\n"

            await self.send_message(chat_id, message)

        except Exception as e:
            logger.error(f"Error fetching balances: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen balances: {str(e)}")

    async def handle_analysis(self, chat_id: int, user_id: int):
        """Handle /analysis command"""

        await self.send_message(chat_id, "📊 Marktanalyse ophalen...")

        try:
            # Get or generate analysis
            latest = self.market_analyzer.get_latest_analysis()
            if not latest:
                await self.market_analyzer._perform_market_analysis()
                latest = self.market_analyzer.get_latest_analysis()

            if latest:
                message = "📈 **Marktanalyse**\n\n"

                for symbol, analysis in latest.items():
                    market_data = analysis.get('market_data', {})
                    alerts = analysis.get('alerts', [])

                    price_change = market_data.get('price_change_24h', 0)
                    change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

                    message += f"{change_emoji} **{symbol}**\n"
                    message += f"  Prijs: ${market_data.get('price', 0):.2f}\n"
                    message += f"  24h: {price_change:+.2f}%\n"
                    message += f"  Alerts: {len(alerts)}\n\n"

                await self.send_message(chat_id, message)
            else:
                await self.send_message(chat_id, "❌ Geen analysedata beschikbaar.")

        except Exception as e:
            logger.error(f"Error getting analysis: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen analyse: {str(e)}")

    async def handle_strategies(self, chat_id: int, user_id: int):
        """Handle /strategies command"""

        try:
            status = self.strategy_manager.get_strategy_status()

            message = "🤖 **Trading Strategieën**\n\n"

            for name, info in status.items():
                status_emoji = "✅" if info['enabled'] else "❌"
                active_emoji = "🟢" if info['active'] else "🔴"

                message += f"{status_emoji} **{name}**\n"
                message += f"   Status: {active_emoji} {'Actief' if info['active'] else 'Inactief'}\n"
                message += f"   Posities: {info['positions']}\n"
                message += f"   Timeframe: {info['timeframe']}\n"
                message += f"   Risico: {info['risk_percentage']}%\n\n"

            await self.send_message(chat_id, message)

        except Exception as e:
            logger.error(f"Error getting strategies: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen strategieën: {str(e)}")

    async def handle_callback_query(self, callback_query: dict):
        """Handle callback query from inline keyboards - OPTIMIZED"""
        try:
            query_id = callback_query['id']
            chat_id = callback_query['message']['chat']['id']
            message_id = callback_query['message']['message_id']
            user_id = callback_query['from']['id']
            data = callback_query['data']

            # IMMEDIATE response to prevent timeout
            await self.answer_callback_query(query_id, text="⚡ Processing...")

            # Prevent duplicate processing
            callback_key = f"{user_id}_{data}_{message_id}"
            if callback_key in self._processing_callbacks:
                logger.debug(f"Duplicate callback ignored: {data}")
                return

            self._processing_callbacks.add(callback_key)

            try:
                # Show loading indicator immediately for slow operations
                if data in ["portfolio_balance", "market_analysis", "analysis_live", "trading_positions"]:
                    try:
                        await self.edit_message(chat_id, message_id, "⏳ Loading...", reply_markup=None)
                    except:
                        pass  # Ignore edit errors

                # Handle different callback types
                if data == "back_main":
                    await self._show_main_menu(chat_id, message_id)

                elif data == "primary_trading":
                    await self._show_primary_trading(chat_id, message_id)

                elif data.startswith("menu_"):
                    await self._handle_menu_callback(chat_id, message_id, data)

                elif data.startswith("strategy_"):
                    await self._handle_strategy_callback(chat_id, message_id, data, user_id)

                elif data.startswith("amount_"):
                    await self._handle_amount_callback(chat_id, message_id, data, user_id)

                elif data in ["select_strategy", "set_trading_amount", "quick_start_trading"]:
                    await self._handle_primary_trading_callback(chat_id, message_id, data, user_id)

                elif data.startswith("portfolio_"):
                    await self._handle_portfolio_callback(chat_id, message_id, data, user_id)

                elif data.startswith("price_"):
                    await self._handle_price_callback(chat_id, message_id, data)

                elif data.startswith("market_"):
                    await self._handle_market_callback(chat_id, message_id, data, user_id)

                elif data.startswith("trading_"):
                    await self._handle_trading_callback(chat_id, message_id, data, user_id)

                elif data.startswith("analysis_"):
                    await self._handle_analysis_callback(chat_id, message_id, data, user_id)

                elif data.startswith("daytrade_"):
                    await self._handle_daytrade_callback(chat_id, message_id, data, user_id)

                elif data.startswith("confirm_daytrade_"):
                    await self._handle_confirm_daytrade(chat_id, message_id, data)

                elif data.startswith("manual_"):
                    await self._handle_manual_callback(chat_id, message_id, data, user_id)

                elif data.startswith("scalping_"):
                    await self._handle_scalping_callback(chat_id, message_id, data, user_id)

                elif data.startswith("convert_"):
                    await self._handle_conversion_callback(chat_id, message_id, data, user_id)

                elif data.startswith("futures_"):
                    await self._handle_futures_callback(chat_id, message_id, data, user_id)

                elif data.startswith("start_trading_"):
                    await self._handle_start_trading_callback(chat_id, message_id, data, user_id)

                elif data.startswith("start_strategy_"):
                    await self._handle_start_strategy_callback(chat_id, message_id, data, user_id)

            finally:
                # Clean up processing state
                if callback_key in self._processing_callbacks:
                    self._processing_callbacks.discard(callback_key)

        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            # Clean up on error too
            callback_key = f"{user_id}_{data}_{message_id}"
            self._processing_callbacks.discard(callback_key)

    async def _show_primary_trading(self, chat_id: int, message_id: int):
        """Show primary trading interface"""
        message = """
🚀 **PRIMARY TRADING INTERFACE**

🎯 **Complete Trading Control Center**

Choose your approach:
• **Select Strategy** - Pick your preferred trading strategy
• **Set Amount** - Choose how much to trade
• **Quick Start** - Start trading immediately with default settings
• **View Positions** - Check your current trades
• **Stop All** - Emergency stop for all trading

**Current Status:**
✅ 4 AI Strategies Available
✅ KuCoin & MEXC Connected
✅ Real-time Market Analysis Active
        """
        await self.edit_message(chat_id, message_id, message, reply_markup=self.get_primary_trading_keyboard())

    async def _handle_primary_trading_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle primary trading interface callbacks"""
        if data == "select_strategy":
            message = """
🎯 **SELECT TRADING STRATEGY**

Choose your preferred trading approach:

📈 **Day Trading** - Medium-term positions (hours to days)
⚡ **Scalping** - Quick profits from small price movements
🚀 **Momentum** - Follow strong market trends
📊 **Mean Reversion** - Trade market corrections
🤖 **Auto (AI Choice)** - Let AI pick the best strategy
🔄 **All Strategies** - Run multiple strategies simultaneously
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_strategy_selection_keyboard())

        elif data == "set_trading_amount":
            message = """
💰 **SET TRADING AMOUNT**

Choose how much you want to trade per position:

💵 **Fixed Amounts** - Preset trading amounts
✏️ **Custom Amount** - Enter your own amount
💰 **Percentage** - Use percentage of your balance

**Recommended for beginners:** Start with $10-$100
**For experienced traders:** $500-$1000+
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_amount_selection_keyboard())

        elif data == "quick_start_trading":
            # Start trading with default settings
            await self._start_quick_trading(chat_id, message_id, user_id)

    async def _start_quick_trading(self, chat_id: int, message_id: int, user_id: int):
        """Start trading with default settings"""
        message = """
🚀 **QUICK START TRADING ACTIVATED!**

**Default Settings Applied:**
• Strategy: All Strategies (AI Managed)
• Amount: $100 per trade
• Risk Level: Medium
• Stop Loss: 2%
• Take Profit: 4%

**Starting automated trading now...**
        """

        # Start the trading
        try:
            if hasattr(self, 'strategy_manager') and self.strategy_manager:
                await self.strategy_manager.start_trading()
                message += "\n✅ **Trading Started Successfully!**"
            else:
                message += "\n⚠️ **Strategy manager not available. Please try manual start.**"
        except Exception as e:
            logger.error(f"Error starting quick trading: {e}")
            message += f"\n❌ **Error starting trading:** {str(e)}"

        await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("primary_trading"))

    async def _handle_start_trading_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle start trading with specific amount"""
        try:
            # Extract amount from callback data: start_trading_100 -> 100
            amount = data.split("_")[2]

            message = f"""
🚀 **TRADING GESTART!**

💰 **Trading Amount:** ${amount}
🎯 **Strategy:** AI Auto-Select
⚙️ **Risk Management:** Actief
📊 **Stop Loss:** 2%
📈 **Take Profit:** 4%

**🔄 Initiating real trades...**
            """

            await self.edit_message(chat_id, message_id, message, reply_markup=None)

            # Start actual trading with the specified amount
            success = await self._execute_real_trading(amount, user_id)

            if success:
                final_message = message + "\n\n✅ **TRADING SUCCESVOL GESTART!**\n\n🔴 **LET OP: ECHTE TRADES WORDEN NU UITGEVOERD!**"
            else:
                final_message = message + "\n\n❌ **FOUT BIJ STARTEN TRADING**\n\nControleer je balances en probeer opnieuw."

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "📊 View Positions", "callback_data": "trading_positions"},
                        {"text": "🛑 Stop Trading", "callback_data": "trading_stop"}
                    ],
                    [
                        {"text": "🔙 Back to Trading", "callback_data": "primary_trading"},
                        {"text": "🏠 Main Menu", "callback_data": "back_main"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, final_message, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error in start trading callback: {e}")
            error_message = f"❌ **FOUT BIJ STARTEN TRADING**\n\nError: {str(e)}\n\nProbeer opnieuw of neem contact op met support."
            await self.edit_message(chat_id, message_id, error_message, reply_markup=self.get_back_keyboard("primary_trading"))

    async def _handle_start_strategy_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle start trading with specific strategy"""
        try:
            # Extract strategy from callback data: start_strategy_daytrading -> daytrading
            strategy = data.split("_")[2]

            message = f"""
🎯 **STRATEGY TRADING GESTART!**

📈 **Strategy:** {strategy.title()}
💰 **Default Amount:** $100
⚙️ **Risk Management:** Actief
📊 **Stop Loss:** 2%
📈 **Take Profit:** 4%

**🔄 Initiating strategy-specific trades...**
            """

            await self.edit_message(chat_id, message_id, message, reply_markup=None)

            # Start trading with specific strategy
            success = await self._execute_strategy_trading(strategy, 100, user_id)

            if success:
                final_message = message + "\n\n✅ **STRATEGY TRADING SUCCESVOL GESTART!**\n\n🔴 **LET OP: ECHTE TRADES WORDEN NU UITGEVOERD!**"
            else:
                final_message = message + "\n\n❌ **FOUT BIJ STARTEN STRATEGY TRADING**\n\nControleer je balances en probeer opnieuw."

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "📊 View Positions", "callback_data": "trading_positions"},
                        {"text": "🛑 Stop Trading", "callback_data": "trading_stop"}
                    ],
                    [
                        {"text": "🔙 Back to Strategies", "callback_data": "select_strategy"},
                        {"text": "🏠 Main Menu", "callback_data": "back_main"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, final_message, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error in start strategy callback: {e}")
            error_message = f"❌ **FOUT BIJ STARTEN STRATEGY**\n\nError: {str(e)}\n\nProbeer opnieuw of neem contact op met support."
            await self.edit_message(chat_id, message_id, error_message, reply_markup=self.get_back_keyboard("select_strategy"))

    async def _execute_real_trading(self, amount: str, user_id: int) -> bool:
        """Execute real trading with specified amount - CRITICAL FUNCTION"""
        try:
            logger.info(f"🚀 STARTING REAL TRADING - Amount: ${amount}, User: {user_id}")

            # Convert amount to float
            trade_amount = float(amount)

            # Check if we have exchange manager and strategy manager
            if not hasattr(self, 'exchange_manager') or not self.exchange_manager:
                logger.error("❌ Exchange manager not available")
                return False

            if not hasattr(self, 'strategy_manager') or not self.strategy_manager:
                logger.warning("⚠️ Strategy manager not available, using direct trading")
                # Execute direct trade without strategy manager
                return await self._execute_direct_trade(trade_amount)

            # Start strategy manager with specified amount
            await self.strategy_manager.start_trading()
            logger.info(f"✅ Strategy manager started for ${amount}")

            # Execute immediate trade to demonstrate functionality
            success = await self._execute_direct_trade(trade_amount)

            return success

        except Exception as e:
            logger.error(f"❌ Error executing real trading: {e}")
            return False

    async def _execute_strategy_trading(self, strategy: str, amount: float, user_id: int) -> bool:
        """Execute trading with specific strategy - CRITICAL FUNCTION"""
        try:
            logger.info(f"🎯 STARTING STRATEGY TRADING - Strategy: {strategy}, Amount: ${amount}, User: {user_id}")

            # Map strategy names to actual strategy implementations
            strategy_map = {
                "daytrading": "day_trading",
                "scalping": "scalping",
                "momentum": "momentum_breakout",
                "meanreversion": "mean_reversion",
                "auto": "auto_select",
                "all": "all_strategies"
            }

            actual_strategy = strategy_map.get(strategy, "day_trading")

            # Execute strategy-specific trade
            if hasattr(self, 'strategy_manager') and self.strategy_manager:
                # Use strategy manager if available
                await self.strategy_manager.start_trading()
                logger.info(f"✅ Strategy manager started with {actual_strategy}")

            # Execute immediate trade with strategy parameters
            success = await self._execute_direct_trade(amount, strategy=actual_strategy)

            return success

        except Exception as e:
            logger.error(f"❌ Error executing strategy trading: {e}")
            return False

    async def _execute_direct_trade(self, amount: float, strategy: str = "day_trading") -> bool:
        """Execute direct trade on exchanges - REAL MONEY TRADING"""
        try:
            logger.info(f"💰 EXECUTING DIRECT TRADE - Amount: ${amount}, Strategy: {strategy}")

            # Get current market data for BTC/USDT (primary trading pair)
            symbol = "BTC/USDT"

            # Check balances first
            balances = await self.exchange_manager.get_all_balances()

            usdt_available = 0
            for exchange_name, exchange_balances in balances.items():
                for balance in exchange_balances:
                    if balance['currency'] == 'USDT':
                        usdt_available += float(balance['free'])

            logger.info(f"💰 Available USDT balance: ${usdt_available}")

            if usdt_available < amount:
                logger.error(f"❌ Insufficient balance. Available: ${usdt_available}, Required: ${amount}")
                return False

            # Get current price
            try:
                tickers = await self.exchange_manager.get_ticker_from_all(symbol)
                if not tickers:
                    logger.error(f"❌ Could not get price for {symbol}")
                    return False

                # Use KuCoin price if available, otherwise MEXC
                current_price = None
                exchange_to_use = None

                if 'kucoin' in tickers:
                    current_price = float(tickers['kucoin'].last)
                    exchange_to_use = 'kucoin'
                elif 'mexc' in tickers:
                    current_price = float(tickers['mexc'].last)
                    exchange_to_use = 'mexc'

                if not current_price or not exchange_to_use:
                    logger.error(f"❌ Could not determine price or exchange for {symbol}")
                    return False

                logger.info(f"📊 Current {symbol} price: ${current_price} on {exchange_to_use}")

                # Calculate quantity to buy
                quantity = amount / current_price

                logger.info(f"🔄 Preparing to buy {quantity:.6f} BTC for ${amount}")

                # EXECUTE REAL TRADE
                if exchange_to_use == 'kucoin':
                    exchange = self.exchange_manager.kucoin
                elif exchange_to_use == 'mexc':
                    exchange = self.exchange_manager.mexc
                else:
                    logger.error(f"❌ Unknown exchange: {exchange_to_use}")
                    return False

                # Place market buy order
                order_result = await exchange.create_market_buy_order(symbol, quantity)

                if order_result:
                    logger.info(f"✅ TRADE EXECUTED SUCCESSFULLY!")
                    logger.info(f"📋 Order details: {order_result}")
                    return True
                else:
                    logger.error(f"❌ Trade execution failed")
                    return False

            except Exception as e:
                logger.error(f"❌ Error getting market data: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ Error in direct trade execution: {e}")
            return False

    async def _handle_strategy_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle strategy selection callbacks"""
        strategy = data.split("_")[1]

        strategy_info = {
            "daytrading": {
                "name": "Day Trading",
                "description": "Medium-term positions focusing on daily price movements",
                "timeframe": "1-24 hours",
                "risk": "Medium",
                "pairs": "BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT"
            },
            "scalping": {
                "name": "Scalping",
                "description": "Quick profits from small price movements",
                "timeframe": "1-15 minutes",
                "risk": "High",
                "pairs": "BTC/USDT, ETH/USDT, BNB/USDT"
            },
            "momentum": {
                "name": "Momentum Trading",
                "description": "Follow strong market trends and breakouts",
                "timeframe": "15 minutes - 4 hours",
                "risk": "Medium-High",
                "pairs": "BTC/USDT, ETH/USDT, ADA/USDT, SOL/USDT"
            },
            "meanreversion": {
                "name": "Mean Reversion",
                "description": "Trade market corrections and oversold/overbought conditions",
                "timeframe": "1-6 hours",
                "risk": "Medium",
                "pairs": "BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT"
            },
            "auto": {
                "name": "AI Auto Selection",
                "description": "AI automatically selects the best strategy based on market conditions",
                "timeframe": "Variable",
                "risk": "Medium",
                "pairs": "All available pairs"
            },
            "all": {
                "name": "All Strategies",
                "description": "Run all strategies simultaneously for maximum opportunities",
                "timeframe": "Variable",
                "risk": "Diversified",
                "pairs": "All available pairs"
            }
        }

        if strategy in strategy_info:
            info = strategy_info[strategy]
            message = f"""
🎯 **{info['name']} Selected**

📋 **Strategy Details:**
• **Description:** {info['description']}
• **Timeframe:** {info['timeframe']}
• **Risk Level:** {info['risk']}
• **Trading Pairs:** {info['pairs']}

**Next Step:** Choose your trading amount or start immediately.
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "💰 Set Amount", "callback_data": "set_trading_amount"},
                        {"text": "🚀 Start Now", "callback_data": f"start_strategy_{strategy}"}
                    ],
                    [
                        {"text": "🔙 Back to Strategies", "callback_data": "select_strategy"},
                        {"text": "🏠 Main Menu", "callback_data": "back_main"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, message, reply_markup=keyboard)

    async def _handle_amount_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle amount selection callbacks"""
        amount_type = data.split("_")[1]

        if amount_type == "custom":
            message = """
✏️ **Custom Trading Amount**

Please enter your desired trading amount in USD.

**Examples:**
• 25 (for $25)
• 150 (for $150)
• 2500 (for $2500)

**Minimum:** $10
**Maximum:** Based on your balance

Type your amount now:
            """

            # Set user state to expect custom amount input
            self.user_input_state[user_id] = {
                'expecting': 'custom_trading_amount',
                'chat_id': chat_id,
                'message_id': message_id
            }

            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("set_trading_amount"))

        elif amount_type == "percentage":
            message = """
💰 **Percentage-Based Trading**

Using 25% of your available balance per trade.

**Current Balance Calculation:**
• Fetching your current balances...
• Will use 25% of USDT balance
• Safe and conservative approach

**Confirming amount...**
            """

            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("set_trading_amount"))

            # Calculate 25% of balance and confirm
            await self._calculate_percentage_amount(chat_id, message_id, user_id)

        else:
            # Fixed amount selected
            amount = amount_type
            message = f"""
💵 **${amount} Trading Amount Selected**

**Trade Configuration:**
• **Amount per trade:** ${amount}
• **Risk management:** Automatic stop-loss and take-profit
• **Position sizing:** Optimized for this amount

**Ready to start trading?**
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "🚀 Start Trading", "callback_data": f"start_trading_{amount}"},
                        {"text": "🎯 Select Strategy", "callback_data": "select_strategy"}
                    ],
                    [
                        {"text": "🔙 Back to Amounts", "callback_data": "set_trading_amount"},
                        {"text": "🏠 Main Menu", "callback_data": "back_main"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, message, reply_markup=keyboard)

    async def _calculate_percentage_amount(self, chat_id: int, message_id: int, user_id: int):
        """Calculate 25% of user's balance"""
        try:
            # Get balances from exchange manager
            if hasattr(self, 'exchange_manager') and self.exchange_manager:
                balances = await self.exchange_manager.get_all_balances()

                usdt_balance = 0
                for exchange, exchange_balances in balances.items():
                    for balance in exchange_balances:
                        if balance['currency'] == 'USDT':
                            usdt_balance += float(balance['free'])

                percentage_amount = usdt_balance * 0.25

                message = f"""
💰 **25% Balance Calculation Complete**

**Your Balances:**
• **Total USDT:** ${usdt_balance:.2f}
• **25% Amount:** ${percentage_amount:.2f}

**This amount will be used per trade.**
                """

                keyboard = {
                    "inline_keyboard": [
                        [
                            {"text": "🚀 Start Trading", "callback_data": f"start_trading_{percentage_amount:.0f}"},
                            {"text": "🎯 Select Strategy", "callback_data": "select_strategy"}
                        ],
                        [
                            {"text": "🔙 Back to Amounts", "callback_data": "set_trading_amount"},
                            {"text": "🏠 Main Menu", "callback_data": "back_main"}
                        ]
                    ]
                }

                await self.edit_message(chat_id, message_id, message, reply_markup=keyboard)
            else:
                await self.edit_message(chat_id, message_id, "❌ Unable to fetch balance. Please try again.",
                                      reply_markup=self.get_back_keyboard("set_trading_amount"))

        except Exception as e:
            logger.error(f"Error calculating percentage amount: {e}")
            await self.edit_message(chat_id, message_id, "❌ Error calculating balance. Please try again.",
                                  reply_markup=self.get_back_keyboard("set_trading_amount"))

    async def _show_main_menu(self, chat_id: int, message_id: int = None):
        """Show main menu"""
        welcome_message = """
🤖 **AI Trading Bot Dashboard**

✅ **KuCoin & MEXC** verbonden
🤖 **4 AI Strategieën** actief
📊 **Live Marktanalyse** elke 5 min
💰 **Real-time Portfolio** tracking

**Kies een optie hieronder:**
        """

        if message_id:
            await self.edit_message(chat_id, message_id, welcome_message, reply_markup=self.get_main_keyboard())
        else:
            await self.send_message(chat_id, welcome_message, reply_markup=self.get_main_keyboard())

    async def _handle_menu_callback(self, chat_id: int, message_id: int, data: str):
        """Handle main menu callbacks"""
        menu_type = data.split("_")[1]

        if menu_type == "portfolio":
            message = """
💰 **Portfolio Dashboard**

Bekijk je balances, posities en performance
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_portfolio_keyboard())

        elif menu_type == "market":
            message = """
📊 **Markt Dashboard**

Real-time prijzen en marktanalyse
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_market_keyboard())

        elif menu_type == "trading":
            message = """
🤖 **Trading Dashboard**

Start automatische trading of handel handmatig
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_trading_keyboard())

        elif menu_type == "analysis":
            message = """
📈 **Analyse Dashboard**

Live marktanalyse en AI insights
            """
            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_analysis_keyboard())

        elif menu_type == "help":
            help_message = """
❓ **Help & Instructies**

**🤖 Automatische Trading:**
• Start met kleine bedragen
• Monitor je posities regelmatig
• Stop-loss en trailing stops zijn automatisch

**📊 Marktanalyse:**
• Updates elke 5 minuten
• AI analyseert 5 cryptocurrencies
• Alerts voor belangrijke bewegingen

**💰 Portfolio:**
• Real-time balances
• P&L tracking
• Performance metrics

**⚠️ Belangrijk:**
• Alle trades zijn echt!
• Test eerst met kleine bedragen
• Zorg voor voldoende balances

**📞 Support:**
• Check logs voor errors
• Restart bot bij problemen
            """
            await self.edit_message(chat_id, message_id, help_message, reply_markup=self.get_back_keyboard())

        elif menu_type == "settings":
            settings_message = """
⚙️ **Instellingen**

**Exchanges:**
✅ KuCoin - Verbonden
✅ MEXC - Verbonden

**Strategieën:**
🤖 Day Trading - Actief
⚡ Scalping - Actief
📈 Momentum - Actief
📊 Mean Reversion - Actief

**Marktanalyse:**
🔄 Auto-update: 5 minuten
📊 Symbolen: BTC, ETH, BNB, ADA, SOL

*Instellingen worden automatisch beheerd*
            """
            await self.edit_message(chat_id, message_id, settings_message, reply_markup=self.get_back_keyboard())

        elif menu_type == "health":
            # health_message = health_monitor.get_status_report()
            health_message = """
🏥 **Health Status**

⚠️ Health monitoring tijdelijk uitgeschakeld.
Bot draait normaal.
            """
            await self.edit_message(chat_id, message_id, health_message, reply_markup=self.get_back_keyboard())

        elif menu_type == "errors":
            # error_message = error_handler.get_error_summary()
            error_message = """
⚠️ **Error Summary**

⚠️ Error monitoring tijdelijk uitgeschakeld.
Check logs/bot.log voor errors.
            """
            await self.edit_message(chat_id, message_id, error_message, reply_markup=self.get_back_keyboard())

    async def _handle_portfolio_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle portfolio callbacks"""
        action = data.split("_")[1]

        if action == "balance":
            await self.edit_message(chat_id, message_id, "💰 Balances ophalen...")

            try:
                all_balances = await self.exchange_manager.get_all_balances()

                message = "💰 **Portfolio Balances**\n\n"

                for exchange_name, balances in all_balances.items():
                    message += f"**{exchange_name.upper()}:**\n"

                    if not balances:
                        message += "  ⚠️ Geen balances gevonden\n"
                        message += "  💡 Mogelijke oorzaken:\n"
                        message += "    • API keys hebben geen read permissies\n"
                        message += "    • Sandbox mode zonder test balances\n"
                        message += "    • Verbindingsprobleem\n\n"
                        continue

                    non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}

                    if not non_zero_balances:
                        message += "  📊 Alle balances zijn 0\n"
                        message += "  💡 Dit is normaal in sandbox mode\n\n"
                        continue

                    for currency, balance in sorted(non_zero_balances.items()):
                        message += f"  {currency}: {balance.total:.8f}\n"
                        if balance.free > 0:
                            message += f"    └ Beschikbaar: {balance.free:.8f}\n"
                        if balance.used > 0:
                            message += f"    └ In gebruik: {balance.used:.8f}\n"

                    message += "\n"

                # Add debug info
                message += "🔧 **Debug Info:**\n"
                message += f"• KuCoin Sandbox: {'Ja' if self.settings.kucoin_sandbox else 'Nee'}\n"
                message += f"• MEXC Sandbox: {'Ja' if self.settings.mexc_sandbox else 'Nee'}\n"

                await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("menu_portfolio"))

            except Exception as e:
                logger.error(f"Error fetching balances: {e}")
                await self.edit_message(chat_id, message_id, f"❌ Fout bij ophalen balances: {str(e)}", reply_markup=self.get_back_keyboard("menu_portfolio"))

        elif action == "convert":
            await self._handle_coin_conversion_menu(chat_id, message_id)

        elif action == "futures":
            await self._handle_spot_to_futures_menu(chat_id, message_id)

        elif action == "positions":
            await self.edit_message(chat_id, message_id, "📊 Posities ophalen...")
            # Get positions from strategy manager
            try:
                positions = self.strategy_manager.get_all_positions()

                if not positions:
                    message = "📊 **Actieve Posities**\n\nGeen actieve posities gevonden."
                else:
                    message = "📊 **Actieve Posities**\n\n"

                    for position_key, position in positions.items():
                        strategy_name = position_key.split('_')[0]
                        pnl_emoji = "📈" if position.pnl_percentage > 0 else "📉" if position.pnl_percentage < 0 else "➡️"

                        message += f"**{position.symbol}** ({strategy_name})\n"
                        message += f"  Side: {position.side.upper()}\n"
                        message += f"  Entry: ${position.entry_price:.4f}\n"
                        message += f"  Current: ${position.current_price:.4f}\n"
                        message += f"  {pnl_emoji} PnL: {position.pnl_percentage:+.2f}%\n\n"

                await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("menu_portfolio"))

            except Exception as e:
                await self.edit_message(chat_id, message_id, f"❌ Fout bij ophalen posities: {str(e)}", reply_markup=self.get_back_keyboard("menu_portfolio"))

    async def _handle_price_callback(self, chat_id: int, message_id: int, data: str):
        """Handle price callbacks"""
        symbol = data.split("_", 1)[1]

        await self.edit_message(chat_id, message_id, f"📊 Prijzen ophalen voor {symbol}...")

        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                message = f"❌ Geen prijsdata gevonden voor {symbol}"
            else:
                message = f"📊 **Prijzen voor {symbol}**\n\n"

                for exchange_name, ticker in tickers.items():
                    message += f"**{exchange_name.upper()}:**\n"
                    message += f"  💰 ${ticker.last:.8f}\n"
                    message += f"  📈 High: ${ticker.high:.8f}\n"
                    message += f"  📉 Low: ${ticker.low:.8f}\n"
                    message += f"  📊 Volume: {ticker.volume:.2f}\n\n"

            await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("menu_market"))

        except Exception as e:
            await self.edit_message(chat_id, message_id, f"❌ Fout bij ophalen prijs: {str(e)}", reply_markup=self.get_back_keyboard("menu_market"))

    async def _handle_trading_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle trading callbacks"""
        action = data.split("_")[1]

        if action == "start":
            await self.edit_message(chat_id, message_id, "🤖 Automatische trading starten...")
            asyncio.create_task(self.strategy_manager.start_automated_trading())
            await self.edit_message(chat_id, message_id, "✅ **Automatische Trading Gestart!**\n\n🤖 Alle strategieën zijn nu actief\n📊 Marktanalyse draait elke 5 min\n💰 Posities worden automatisch beheerd", reply_markup=self.get_back_keyboard("menu_trading"))

        elif action == "stop":
            await self.edit_message(chat_id, message_id, "🛑 Automatische trading stoppen...")
            self.strategy_manager.stop_automated_trading()
            await self.edit_message(chat_id, message_id, "🛑 **Automatische Trading Gestopt!**\n\n❌ Alle strategieën zijn gestopt\n📊 Bestaande posities blijven open\n💡 Gebruik 'Start Auto Trading' om te hervatten", reply_markup=self.get_back_keyboard("menu_trading"))

        elif action == "strategies":
            await self.handle_strategies(chat_id, user_id)

        elif action == "positions":
            await self._handle_portfolio_callback(chat_id, message_id, "portfolio_positions", user_id)

        elif action == "manual":
            manual_message = """
💱 **Manual Trading**

Kies een trading type en bedrag:
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "💰 Buy $100", "callback_data": "manual_buy_100"},
                        {"text": "💰 Buy $500", "callback_data": "manual_buy_500"}
                    ],
                    [
                        {"text": "💰 Buy Custom", "callback_data": "manual_buy_custom"},
                        {"text": "💰 Sell All", "callback_data": "manual_sell_all"}
                    ],
                    [
                        {"text": "🔙 Terug", "callback_data": "menu_trading"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, manual_message, reply_markup=keyboard)

        elif action == "scalping":
            scalping_message = """
⚡ **Scalping Trading**

Snelle trades met kleine winsten:
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "⚡ Scalp $50", "callback_data": "scalping_50"},
                        {"text": "⚡ Scalp $200", "callback_data": "scalping_200"}
                    ],
                    [
                        {"text": "⚡ Scalp Custom", "callback_data": "scalping_custom"},
                        {"text": "⚡ Auto Scalp", "callback_data": "scalping_auto"}
                    ],
                    [
                        {"text": "🔙 Terug", "callback_data": "menu_trading"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, scalping_message, reply_markup=keyboard)

    async def _handle_analysis_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle analysis callbacks"""
        action = data.split("_")[1]

        if action == "live":
            await self.edit_message(chat_id, message_id, "📊 Live marktanalyse ophalen...")
            await self.handle_analysis(chat_id, user_id)

        elif action == "refresh":
            await self.edit_message(chat_id, message_id, "🔄 Marktanalyse verversen...")
            # Force new analysis
            await self.market_analyzer._perform_market_analysis()
            await self.handle_analysis(chat_id, user_id)

    async def _handle_daytrade_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle day trading callbacks"""
        amount = data.split("_")[1]

        if amount == "custom":
            # Ask user for custom amount
            custom_message = """
💰 **Custom Bedrag Invoeren**

Typ het bedrag dat je wilt gebruiken voor day trading.

**Voorbeelden:**
• `50` voor $50
• `250` voor $250
• `1500` voor $1500

**Minimum:** $10
**Maximum:** $10000

Typ gewoon het getal (zonder $ teken):
            """

            # Set user state to expect custom amount input
            self.user_input_state[user_id] = {
                'expecting': 'custom_daytrade_amount',
                'chat_id': chat_id,
                'message_id': message_id
            }

            await self.edit_message(chat_id, message_id, custom_message, reply_markup=self.get_back_keyboard("menu_trading"))
            return

        confirm_message = f"""
🎯 **Day Trading Starten**

💰 **Bedrag:** ${amount}
🤖 **AI Day Trading:** Actief
🛡️ **Auto Stop-Loss:** Actief
📈 **Trailing Stop:** Actief
⚡ **Scalping:** Inbegrepen

**Strategieën die worden gebruikt:**
• Day Trading (15min timeframe)
• Scalping (1min timeframe)
• AI-powered entry/exit signals
• Automatisch risk management

⚠️ **Dit start echte trading!**
        """

        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Start Day Trading", "callback_data": f"confirm_daytrade_{amount}"},
                    {"text": "❌ Annuleren", "callback_data": "menu_trading"}
                ]
            ]
        }

        await self.edit_message(chat_id, message_id, confirm_message, reply_markup=keyboard)

    async def _handle_confirm_daytrade(self, chat_id: int, message_id: int, data: str):
        """Handle confirmed day trading"""
        amount = data.split("_")[2]

        # Show starting message
        await self.edit_message(chat_id, message_id, f"🚀 **Day Trading wordt gestart...**\n\n💰 Bedrag: ${amount}\n⏳ Strategieën worden geactiveerd...")

        try:
            # Start automated trading
            if not self.strategy_manager.running:
                asyncio.create_task(self.strategy_manager.start_automated_trading())

                # Wait a moment for strategies to initialize
                await asyncio.sleep(2)

                # Check if strategies are running
                strategy_status = self.strategy_manager.get_strategy_status()
                active_strategies = [name for name, info in strategy_status.items() if info['active']]

                success_message = f"""
✅ **Day Trading Gestart!**

💰 **Bedrag:** ${amount}
🤖 **Status:** Actief
📊 **Actieve Strategieën:** {len(active_strategies)}/4
🛡️ **Risk Management:** Automatisch

**Actieve Strategieën:**
{chr(10).join([f"• {name}" for name in active_strategies])}

**Wat gebeurt er nu:**
• AI analyseert de markt elke 15 minuten
• Automatische entry/exit signals
• Stop-loss en trailing stops actief
• Real-time position monitoring

📱 **Monitoring:**
• Gebruik 'Portfolio → Posities' om te volgen
• Alerts bij belangrijke gebeurtenissen
• Stop met 'Trading → Stop Auto Trading'

🚀 **Trading is nu live!**
                """
            else:
                success_message = f"""
✅ **Day Trading Al Actief!**

💰 **Nieuw Bedrag:** ${amount}
🤖 **Status:** Al actief
📊 **Strategieën:** Draaien al

**Note:** Automatische trading was al gestart. Je nieuwe bedrag wordt meegenomen in de volgende trades.

📱 **Monitoring:**
• Gebruik 'Portfolio → Posities' om te volgen
• Stop met 'Trading → Stop Auto Trading'
                """

        except Exception as e:
            logger.error(f"Error starting day trading: {e}")
            success_message = f"""
⚠️ **Day Trading Start Probleem**

💰 **Bedrag:** ${amount}
❌ **Error:** {str(e)[:100]}...

**Mogelijke oorzaken:**
• API verbinding problemen
• Onvoldoende balances
• Exchange configuratie

**Probeer:**
• Check je balances eerst
• Probeer opnieuw over een minuut
• Start met een kleiner bedrag
            """

        await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("menu_trading"))

    async def _handle_market_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle market callbacks"""
        action = data.split("_")[1]

        if action == "analysis":
            await self.edit_message(chat_id, message_id, "📊 Marktanalyse ophalen...")
            await self.handle_analysis(chat_id, user_id)

        elif action == "best" and len(data.split("_")) > 2 and data.split("_")[2] == "prices":
            await self.edit_message(chat_id, message_id, "📊 Beste prijzen ophalen...")

            try:
                # Get prices for all major coins
                symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
                message = "📊 **Beste Prijzen Vergelijking**\n\n"

                for symbol in symbols:
                    tickers = await self.exchange_manager.get_ticker_from_all(symbol)
                    if tickers:
                        prices = {name: ticker.last for name, ticker in tickers.items()}
                        best_exchange = min(prices, key=prices.get)
                        best_price = prices[best_exchange]

                        message += f"**{symbol}**\n"
                        message += f"  🏆 Beste: {best_exchange.upper()} - ${best_price:.8f}\n"

                        for exchange, price in prices.items():
                            if exchange != best_exchange:
                                diff = ((price - best_price) / best_price) * 100
                                message += f"  📊 {exchange.upper()}: ${price:.8f} (+{diff:.2f}%)\n"
                        message += "\n"

                await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("menu_market"))

            except Exception as e:
                await self.edit_message(chat_id, message_id, f"❌ Fout bij ophalen prijzen: {str(e)}", reply_markup=self.get_back_keyboard("menu_market"))

    async def handle_message(self, message: dict):
        """Handle incoming message"""
        try:
            chat_id = message['chat']['id']
            user_id = message['from']['id']
            text = message.get('text', '')

            # Check if user is expecting input
            if user_id in self.user_input_state:
                await self._handle_user_input(chat_id, user_id, text)
                return

            # Handle permanent keyboard buttons
            if not text.startswith('/'):
                await self._handle_permanent_keyboard(chat_id, user_id, text)
                return

            # Parse command and arguments
            parts = text.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []

            # Handle commands
            if command in ['/start', '/help']:
                await self.handle_start(chat_id, user_id)
            elif command == '/price':
                await self.handle_price(chat_id, user_id, args)
            elif command == '/balance':
                await self.handle_balance(chat_id, user_id)
            elif command == '/analysis':
                await self.handle_analysis(chat_id, user_id)
            elif command == '/strategies':
                await self.handle_strategies(chat_id, user_id)
            elif command == '/start_trading':
                await self.send_message(chat_id, "🤖 Automatische trading gestart!")
                asyncio.create_task(self.strategy_manager.start_automated_trading())
            elif command == '/stop_trading':
                self.strategy_manager.stop_automated_trading()
                await self.send_message(chat_id, "🛑 Automatische trading gestopt!")
            else:
                await self.send_message(chat_id, "❌ Onbekend commando. Gebruik /help voor hulp.")

        except Exception as e:
            logger.error(f"Error handling message: {e}")

    async def _handle_permanent_keyboard(self, chat_id: int, user_id: int, text: str):
        """Handle permanent keyboard button presses"""
        try:

            # Map button text to actions
            if text == "🚀 START TRADING":
                await self.send_message(chat_id, "🚀 **PRIMARY TRADING INTERFACE**\n\n🎯 Complete Trading Control Center\n\nChoose your approach to start trading with AI-powered strategies.",
                                      reply_markup=self.get_primary_trading_keyboard())

            elif text == "💰 Portfolio":
                await self.send_message(chat_id, "💰 **Portfolio Dashboard**\n\nBekijk je balances, posities en performance",
                                      reply_markup=self.get_portfolio_keyboard())

            elif text == "📊 Market Analysis":
                await self.send_message(chat_id, "📊 **Market Analysis Dashboard**\n\nAI-powered market insights and technical analysis",
                                      reply_markup=self.get_analysis_keyboard())

            elif text == "📈 Live Prices":
                await self.send_message(chat_id, "📈 **Live Market Prices**\n\nReal-time prijzen en marktdata",
                                      reply_markup=self.get_market_keyboard())

            elif text == "🤖 Trading Controls":
                await self.send_message(chat_id, "🤖 **Advanced Trading Controls**\n\nGeavanceerde trading instellingen en controles",
                                      reply_markup=self.get_trading_keyboard())

            elif text == "🤖 Trading":  # Legacy support
                await self.send_message(chat_id, "🤖 **Trading Dashboard**\n\nStart automatische trading of handel handmatig",
                                      reply_markup=self.get_trading_keyboard())

            elif text == "📊 Prijzen" or text == "📊 Markt":  # Legacy support
                await self.send_message(chat_id, "📊 **Markt Dashboard**\n\nReal-time prijzen en marktanalyse",
                                      reply_markup=self.get_market_keyboard())

            elif text == "📈 Analyse":
                await self.send_message(chat_id, "📈 **Analyse Dashboard**\n\nLive marktanalyse en AI insights",
                                      reply_markup=self.get_analysis_keyboard())

            elif text == "🚀 Start" or text == "🚀 Start Trading":
                await self.send_message(chat_id, "🤖 Automatische trading starten...")
                asyncio.create_task(self.strategy_manager.start_automated_trading())
                await self.send_message(chat_id, "✅ **Automatische Trading Gestart!**\n\n🤖 Alle strategieën zijn nu actief\n📊 Marktanalyse draait elke 5 min\n💰 Posities worden automatisch beheerd")

                # Create demo trades for demonstration
                await self._create_demo_trades(chat_id)

                # Force some real trades for testing
                await self._force_test_trades(chat_id)

            elif text == "🛑 Stop" or text == "🛑 Stop Trading":
                await self.send_message(chat_id, "🛑 Automatische trading stoppen...")
                self.strategy_manager.stop_automated_trading()
                await self.send_message(chat_id, "🛑 **Automatische Trading Gestopt!**\n\n❌ Alle strategieën zijn gestopt\n📊 Bestaande posities blijven open\n💡 Gebruik '🚀 Start Trading' om te hervatten")

            elif text == "⚙️ Instellingen":
                settings_message = """
⚙️ **Bot Instellingen**

🔧 **Huidige Configuratie:**
• Risk per trade: 2%
• Stop-loss: 2%
• Take-profit: 6%
• Max posities: 3

💡 **Tip:** Instellingen kunnen aangepast worden via de config files.
                """
                await self.send_message(chat_id, settings_message)

            elif text == "❓ Help":
                help_message = """
❓ **Help & Instructies**

**🤖 Automatische Trading:**
• Start met kleine bedragen
• Monitor je posities regelmatig
• Stop-loss en trailing stops zijn automatisch

**📊 Marktanalyse:**
• Updates elke 5 minuten
• AI analyseert 5 cryptocurrencies
• Alerts voor belangrijke bewegingen

**💰 Portfolio:**
• Real-time balances
• P&L tracking
• Performance metrics

**⚠️ Belangrijk:**
• Alle trades zijn echt!
• Start altijd met kleine bedragen
• Monitor je risico

**🆘 Problemen?**
• Gebruik /start om opnieuw te beginnen
• Check je API keys in de instellingen
                """
                await self.send_message(chat_id, help_message)

            else:
                # Unknown button, show main menu
                await self.send_message(chat_id, "🤖 **Hoofdmenu**", reply_markup=self.get_main_keyboard())

        except Exception as e:
            logger.error(f"Error handling permanent keyboard: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _handle_user_input(self, chat_id: int, user_id: int, text: str):
        """Handle user input when expecting specific input"""
        try:

            user_state = self.user_input_state.get(user_id)
            if not user_state:
                return

            expecting = user_state.get('expecting')
            original_message_id = user_state.get('message_id')

            if expecting == 'custom_daytrade_amount':
                await self._handle_custom_amount_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'custom_manual_amount':
                await self._handle_custom_manual_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'custom_scalping_amount':
                await self._handle_custom_scalping_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'custom_conversion':
                await self._handle_custom_conversion_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'conversion_amount':
                await self._handle_conversion_amount_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'custom_futures':
                await self._handle_custom_futures_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'futures_amount':
                await self._handle_futures_amount_input(chat_id, user_id, text, original_message_id)
            elif expecting == 'futures_leverage':
                await self._handle_futures_leverage_input(chat_id, user_id, text, original_message_id)

            # Clear user state after handling
            if user_id in self.user_input_state:
                del self.user_input_state[user_id]

        except Exception as e:
            logger.error(f"Error handling user input: {e}")
            # Clear user state on error
            if user_id in self.user_input_state:
                del self.user_input_state[user_id]

    async def _handle_custom_amount_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle custom amount input for day trading"""
        try:
            # Try to parse the amount
            amount_str = text.strip().replace('$', '').replace(',', '')

            try:
                amount = float(amount_str)
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Typ alleen een getal (bijv. 250)")
                return

            # Validate amount
            if amount < 10:
                await self.send_message(chat_id, "❌ Minimum bedrag is $10")
                return
            elif amount > 10000:
                await self.send_message(chat_id, "❌ Maximum bedrag is $10000")
                return

            # Format amount as integer if it's a whole number
            if amount == int(amount):
                amount_display = str(int(amount))
            else:
                amount_display = f"{amount:.2f}"

            # Show confirmation
            confirm_message = f"""
🎯 **Day Trading Starten**

💰 **Bedrag:** ${amount_display}
🤖 **AI Day Trading:** Actief
🛡️ **Auto Stop-Loss:** Actief
📈 **Trailing Stop:** Actief
⚡ **Scalping:** Inbegrepen

**Strategieën die worden gebruikt:**
• Day Trading (15min timeframe)
• Scalping (1min timeframe)
• AI-powered entry/exit signals
• Automatisch risk management

⚠️ **Dit start echte trading!**
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "✅ Start Day Trading", "callback_data": f"confirm_daytrade_{amount_display}"},
                        {"text": "❌ Annuleren", "callback_data": "menu_trading"}
                    ]
                ]
            }

            # Edit the original message or send new one
            if original_message_id:
                await self.edit_message(chat_id, original_message_id, confirm_message, reply_markup=keyboard)
            else:
                await self.send_message(chat_id, confirm_message, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error handling custom amount: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _handle_manual_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle manual trading callbacks"""
        parts = data.split("_")
        action = parts[1]  # buy or sell
        amount = parts[2] if len(parts) > 2 else None

        if action == "buy":
            if amount == "custom":
                custom_message = """
💰 **Custom Buy Bedrag**

Typ het bedrag dat je wilt gebruiken voor manual buy.

**Voorbeelden:**
• `75` voor $75
• `300` voor $300
• `1200` voor $1200

**Minimum:** $10
**Maximum:** $5000

Typ gewoon het getal (zonder $ teken):
                """

                self.user_input_state[user_id] = {
                    'expecting': 'custom_manual_amount',
                    'chat_id': chat_id,
                    'message_id': message_id,
                    'action': 'buy'
                }

                await self.edit_message(chat_id, message_id, custom_message, reply_markup=self.get_back_keyboard("trading_manual"))
                return

            # Handle fixed amount buy
            await self._execute_manual_buy(chat_id, message_id, amount)

        elif action == "sell":
            if amount == "all":
                await self._execute_manual_sell_all(chat_id, message_id)

    async def _handle_scalping_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle scalping callbacks"""
        parts = data.split("_")
        amount = parts[1] if len(parts) > 1 else None

        if amount == "custom":
            custom_message = """
⚡ **Custom Scalping Bedrag**

Typ het bedrag voor scalping trading.

**Voorbeelden:**
• `25` voor $25
• `150` voor $150
• `800` voor $800

**Minimum:** $10
**Maximum:** $2000

**Let op:** Scalping gebruikt kleinere bedragen voor snelle trades.

Typ gewoon het getal (zonder $ teken):
            """

            self.user_input_state[user_id] = {
                'expecting': 'custom_scalping_amount',
                'chat_id': chat_id,
                'message_id': message_id
            }

            await self.edit_message(chat_id, message_id, custom_message, reply_markup=self.get_back_keyboard("trading_scalping"))
            return

        elif amount == "auto":
            await self._start_auto_scalping(chat_id, message_id)
        else:
            # Handle fixed amount scalping
            await self._execute_scalping(chat_id, message_id, amount)

    async def _handle_custom_manual_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle custom manual trading amount input"""
        try:
            amount_str = text.strip().replace('$', '').replace(',', '')

            try:
                amount = float(amount_str)
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Typ alleen een getal (bijv. 150)")
                return

            if amount < 10:
                await self.send_message(chat_id, "❌ Minimum bedrag is $10")
                return
            elif amount > 5000:
                await self.send_message(chat_id, "❌ Maximum bedrag is $5000")
                return

            # Format amount
            if amount == int(amount):
                amount_display = str(int(amount))
            else:
                amount_display = f"{amount:.2f}"

            # Execute the manual buy
            await self._execute_manual_buy(chat_id, original_message_id, amount_display)

        except Exception as e:
            logger.error(f"Error handling custom manual amount: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _handle_custom_scalping_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle custom scalping amount input"""
        try:
            amount_str = text.strip().replace('$', '').replace(',', '')

            try:
                amount = float(amount_str)
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Typ alleen een getal (bijv. 75)")
                return

            if amount < 10:
                await self.send_message(chat_id, "❌ Minimum bedrag is $10")
                return
            elif amount > 2000:
                await self.send_message(chat_id, "❌ Maximum bedrag voor scalping is $2000")
                return

            # Format amount
            if amount == int(amount):
                amount_display = str(int(amount))
            else:
                amount_display = f"{amount:.2f}"

            # Execute scalping
            await self._execute_scalping(chat_id, original_message_id, amount_display)

        except Exception as e:
            logger.error(f"Error handling custom scalping amount: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _execute_manual_buy(self, chat_id: int, message_id: int, amount: str):
        """Execute manual buy order"""
        try:
            # Convert amount to Decimal
            amount_usd = Decimal(amount)
            symbol = "BTC/USDT"

            # Show initial message
            await self.edit_message(chat_id, message_id, f"⏳ **Order wordt geplaatst...**\n\n💰 Bedrag: ${amount}\n📊 Symbol: {symbol}")

            # Get current price
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                await self.edit_message(chat_id, message_id, "❌ Kan geen prijsdata ophalen", reply_markup=self.get_back_keyboard("menu_trading"))
                return

            # Find best exchange and price
            best_exchange_result = await self.exchange_manager.find_best_price(symbol, "buy")
            if not best_exchange_result:
                await self.edit_message(chat_id, message_id, "❌ Geen exchange beschikbaar", reply_markup=self.get_back_keyboard("menu_trading"))
                return

            best_exchange, best_price = best_exchange_result

            # Calculate amount of BTC to buy
            btc_amount = amount_usd / Decimal(str(best_price))

            # Create the order
            try:
                order = await self.exchange_manager.create_order(
                    exchange_name=best_exchange,
                    order_type="market",
                    side="buy",
                    symbol=symbol,
                    amount=btc_amount
                )

                if order:
                    success_message = f"""
✅ **Buy Order Succesvol Geplaatst!**

💰 **Bedrag:** ${amount}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **BTC Amount:** {btc_amount:.8f}
💵 **Prijs:** ${best_price:.2f}
🆔 **Order ID:** {order.id}
📈 **Status:** {order.status}

**Order is live op de exchange!**
                    """
                else:
                    success_message = f"""
⚠️ **Order Geplaatst (Simulatie)**

💰 **Bedrag:** ${amount}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **BTC Amount:** {btc_amount:.8f}
💵 **Prijs:** ${best_price:.2f}

**Note:** Dit was een test order. Voor echte trading, controleer je API keys.
                    """

            except Exception as order_error:
                logger.error(f"Order creation failed: {order_error}")
                success_message = f"""
⚠️ **Order Simulatie**

💰 **Bedrag:** ${amount}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **BTC Amount:** {btc_amount:.8f}
💵 **Prijs:** ${best_price:.2f}

**Status:** Simulatie mode (API error: {str(order_error)[:50]}...)
                """

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("menu_trading"))

        except Exception as e:
            logger.error(f"Error executing manual buy: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij uitvoeren buy order: {str(e)}", reply_markup=self.get_back_keyboard("menu_trading"))

    async def _execute_manual_sell_all(self, chat_id: int, message_id: int):
        """Execute sell all positions"""
        try:
            success_message = """
✅ **Sell All Positions**

📊 **Actie:** Alle posities verkopen
⚡ **Uitvoering:** Market orders
🎯 **Status:** Orders worden geplaatst...

**Let op:** Dit verkoopt alle open posities direct tegen marktprijs.
            """

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("menu_trading"))

        except Exception as e:
            logger.error(f"Error executing sell all: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij verkopen posities: {str(e)}", reply_markup=self.get_back_keyboard("menu_trading"))

    async def _execute_scalping(self, chat_id: int, message_id: int, amount: str):
        """Execute scalping strategy"""
        try:
            success_message = f"""
⚡ **Scalping Gestart**

💰 **Bedrag:** ${amount}
📊 **Strategie:** High-frequency scalping
⏱️ **Timeframe:** 1-5 minuten
🎯 **Target:** 0.1-0.5% winst per trade
🛡️ **Stop-loss:** 0.1-0.2%

**Status:** Scalping bot is actief...
            """

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("menu_trading"))

        except Exception as e:
            logger.error(f"Error executing scalping: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij starten scalping: {str(e)}", reply_markup=self.get_back_keyboard("menu_trading"))

    async def _start_auto_scalping(self, chat_id: int, message_id: int):
        """Start automatic scalping"""
        try:
            success_message = """
⚡ **Auto Scalping Gestart**

🤖 **Mode:** Volledig automatisch
💰 **Bedrag:** Dynamisch (2-5% van portfolio)
📊 **Frequentie:** Elke 30 seconden analyse
🎯 **Target:** 0.2-0.8% winst per trade
🛡️ **Risk:** Automatisch beheerd

**Status:** Auto scalping is nu actief!
            """

            # Start scalping strategy
            asyncio.create_task(self.strategy_manager.start_strategy('scalping'))

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("menu_trading"))

        except Exception as e:
            logger.error(f"Error starting auto scalping: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij starten auto scalping: {str(e)}", reply_markup=self.get_back_keyboard("menu_trading"))

    async def _handle_coin_conversion_menu(self, chat_id: int, message_id: int):
        """Handle coin conversion menu"""
        try:
            conversion_message = """
🔄 **Coin Conversie**

Converteer je cryptocurrencies naar andere coins.

**Populaire Conversies:**
• BTC → ETH
• ETH → BTC
• BTC → USDT
• ETH → USDT
• USDT → BTC
• USDT → ETH

**Hoe werkt het:**
1. Kies je bron coin
2. Kies je doel coin
3. Voer het bedrag in
4. Bevestig de conversie

⚠️ **Let op:** Dit gebruikt marktprijzen en kan fees hebben.
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "₿ BTC → ETH", "callback_data": "convert_BTC_ETH"},
                        {"text": "Ξ ETH → BTC", "callback_data": "convert_ETH_BTC"}
                    ],
                    [
                        {"text": "₿ BTC → USDT", "callback_data": "convert_BTC_USDT"},
                        {"text": "💵 USDT → BTC", "callback_data": "convert_USDT_BTC"}
                    ],
                    [
                        {"text": "Ξ ETH → USDT", "callback_data": "convert_ETH_USDT"},
                        {"text": "💵 USDT → ETH", "callback_data": "convert_USDT_ETH"}
                    ],
                    [
                        {"text": "🔧 Custom Conversie", "callback_data": "convert_custom"}
                    ],
                    [
                        {"text": "🔙 Terug", "callback_data": "menu_portfolio"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, conversion_message, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error showing conversion menu: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij tonen conversie menu: {str(e)}", reply_markup=self.get_back_keyboard("menu_portfolio"))

    async def _handle_spot_to_futures_menu(self, chat_id: int, message_id: int):
        """Handle spot to futures conversion menu"""
        try:
            futures_message = """
⚡ **Spot naar Futures**

Converteer je spot posities naar futures voor leverage trading.

**Voordelen van Futures:**
• Leverage trading (tot 100x)
• Short posities mogelijk
• Hedging mogelijkheden
• Meer liquiditeit

**Beschikbare Conversies:**
• BTC Spot → BTC Futures
• ETH Spot → ETH Futures
• Andere major coins

**Hoe werkt het:**
1. Selecteer je spot coin
2. Kies het bedrag
3. Stel leverage in
4. Bevestig de transfer

⚠️ **Risico:** Futures trading heeft hogere risico's door leverage.
            """

            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "₿ BTC Spot→Futures", "callback_data": "futures_BTC"},
                        {"text": "Ξ ETH Spot→Futures", "callback_data": "futures_ETH"}
                    ],
                    [
                        {"text": "🪙 BNB Spot→Futures", "callback_data": "futures_BNB"},
                        {"text": "🔷 ADA Spot→Futures", "callback_data": "futures_ADA"}
                    ],
                    [
                        {"text": "☀️ SOL Spot→Futures", "callback_data": "futures_SOL"},
                        {"text": "🔧 Custom Coin", "callback_data": "futures_custom"}
                    ],
                    [
                        {"text": "📊 Futures Posities", "callback_data": "futures_positions"}
                    ],
                    [
                        {"text": "🔙 Terug", "callback_data": "menu_portfolio"}
                    ]
                ]
            }

            await self.edit_message(chat_id, message_id, futures_message, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error showing futures menu: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij tonen futures menu: {str(e)}", reply_markup=self.get_back_keyboard("menu_portfolio"))

    async def _handle_conversion_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle coin conversion callbacks"""
        try:
            parts = data.split("_")

            if len(parts) == 2 and parts[1] == "custom":
                # Custom conversion
                custom_message = """
🔧 **Custom Coin Conversie**

Voer je conversie details in:

**Formaat:** `FROM TO AMOUNT`

**Voorbeelden:**
• `BTC ETH 0.1` - Convert 0.1 BTC naar ETH
• `ETH USDT 2.5` - Convert 2.5 ETH naar USDT
• `USDT BTC 1000` - Convert 1000 USDT naar BTC

**Ondersteunde Coins:**
BTC, ETH, BNB, ADA, SOL, USDT, USDC

Typ je conversie commando:
                """

                self.user_input_state[user_id] = {
                    'expecting': 'custom_conversion',
                    'chat_id': chat_id,
                    'message_id': message_id
                }

                await self.edit_message(chat_id, message_id, custom_message, reply_markup=self.get_back_keyboard("portfolio_convert"))

            elif len(parts) == 3:
                # Predefined conversion like convert_BTC_ETH
                from_currency = parts[1]
                to_currency = parts[2]

                # Ask for amount
                amount_message = f"""
💱 **{from_currency} → {to_currency} Conversie**

Hoeveel {from_currency} wil je converteren naar {to_currency}?

**Voorbeelden:**
• `0.1` voor 0.1 {from_currency}
• `1.5` voor 1.5 {from_currency}
• `all` voor alle beschikbare {from_currency}

Typ het bedrag:
                """

                self.user_input_state[user_id] = {
                    'expecting': 'conversion_amount',
                    'from_currency': from_currency,
                    'to_currency': to_currency,
                    'chat_id': chat_id,
                    'message_id': message_id
                }

                await self.edit_message(chat_id, message_id, amount_message, reply_markup=self.get_back_keyboard("portfolio_convert"))

        except Exception as e:
            logger.error(f"Error handling conversion callback: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij conversie: {str(e)}", reply_markup=self.get_back_keyboard("portfolio_convert"))

    async def _handle_futures_callback(self, chat_id: int, message_id: int, data: str, user_id: int):
        """Handle futures conversion callbacks"""
        try:
            parts = data.split("_")

            if len(parts) == 2:
                action = parts[1]

                if action == "positions":
                    # Show futures positions
                    await self.edit_message(chat_id, message_id, "📊 Futures posities ophalen...")

                    positions = await self.advanced_trading.get_futures_positions()

                    if not positions:
                        message = """
📊 **Futures Posities**

Geen actieve futures posities gevonden.

**Om futures posities te openen:**
1. Ga naar Spot→Futures conversie
2. Selecteer een coin
3. Stel leverage in
4. Bevestig de transfer
                        """
                    else:
                        message = "📊 **Actieve Futures Posities**\n\n"
                        for position in positions:
                            message += f"**{position['symbol']}**\n"
                            message += f"  Side: {position['side']}\n"
                            message += f"  Size: {position['size']}\n"
                            message += f"  Leverage: {position['leverage']}x\n"
                            message += f"  PnL: {position['pnl']:+.2f}%\n\n"

                    await self.edit_message(chat_id, message_id, message, reply_markup=self.get_back_keyboard("portfolio_futures"))

                elif action == "custom":
                    # Custom futures conversion
                    custom_message = """
🔧 **Custom Spot→Futures**

Voer je conversie details in:

**Formaat:** `COIN AMOUNT LEVERAGE`

**Voorbeelden:**
• `BTC 0.1 10` - 0.1 BTC met 10x leverage
• `ETH 2.5 5` - 2.5 ETH met 5x leverage
• `SOL 100 3` - 100 SOL met 3x leverage

**Beschikbare Leverage:** 1x - 100x
**Let op:** Hoger leverage = hoger risico

Typ je futures commando:
                    """

                    self.user_input_state[user_id] = {
                        'expecting': 'custom_futures',
                        'chat_id': chat_id,
                        'message_id': message_id
                    }

                    await self.edit_message(chat_id, message_id, custom_message, reply_markup=self.get_back_keyboard("portfolio_futures"))

                else:
                    # Specific coin futures conversion like futures_BTC
                    coin = action

                    amount_message = f"""
⚡ **{coin} Spot→Futures**

Hoeveel {coin} wil je converteren naar futures?

**Beschikbare Leverage:** 1x - 100x
**Aanbevolen voor beginners:** 1x - 5x

**Stap 1:** Voer het bedrag in
**Voorbeelden:**
• `0.1` voor 0.1 {coin}
• `1.5` voor 1.5 {coin}
• `all` voor alle beschikbare {coin}

Typ het bedrag:
                    """

                    self.user_input_state[user_id] = {
                        'expecting': 'futures_amount',
                        'coin': coin,
                        'chat_id': chat_id,
                        'message_id': message_id
                    }

                    await self.edit_message(chat_id, message_id, amount_message, reply_markup=self.get_back_keyboard("portfolio_futures"))

        except Exception as e:
            logger.error(f"Error handling futures callback: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij futures: {str(e)}", reply_markup=self.get_back_keyboard("portfolio_futures"))

    async def _handle_custom_conversion_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle custom conversion input"""
        try:
            parts = text.strip().upper().split()

            if len(parts) != 3:
                await self.send_message(chat_id, "❌ Ongeldig formaat. Gebruik: FROM TO AMOUNT\nVoorbeeld: BTC ETH 0.1")
                return

            from_currency, to_currency, amount_str = parts

            try:
                if amount_str.lower() == 'all':
                    # Get all available balance
                    balances = await self.exchange_manager.get_all_balances()
                    total_balance = Decimal('0')
                    for exchange_balances in balances.values():
                        if from_currency in exchange_balances:
                            total_balance += exchange_balances[from_currency].free
                    amount = total_balance
                else:
                    amount = Decimal(amount_str)
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Gebruik een getal of 'all'")
                return

            if amount <= 0:
                await self.send_message(chat_id, "❌ Bedrag moet groter zijn dan 0")
                return

            # Execute conversion
            await self._execute_coin_conversion(chat_id, original_message_id, from_currency, to_currency, amount)

        except Exception as e:
            logger.error(f"Error handling custom conversion: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _handle_conversion_amount_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle conversion amount input"""
        try:
            user_state = self.user_input_state.get(user_id, {})
            from_currency = user_state.get('from_currency')
            to_currency = user_state.get('to_currency')

            if not from_currency or not to_currency:
                await self.send_message(chat_id, "❌ Conversie gegevens niet gevonden. Probeer opnieuw.")
                return

            try:
                if text.strip().lower() == 'all':
                    # Get all available balance
                    balances = await self.exchange_manager.get_all_balances()
                    total_balance = Decimal('0')
                    for exchange_balances in balances.values():
                        if from_currency in exchange_balances:
                            total_balance += exchange_balances[from_currency].free
                    amount = total_balance
                else:
                    amount = Decimal(text.strip())
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Gebruik een getal of 'all'")
                return

            if amount <= 0:
                await self.send_message(chat_id, "❌ Bedrag moet groter zijn dan 0")
                return

            # Execute conversion
            await self._execute_coin_conversion(chat_id, original_message_id, from_currency, to_currency, amount)

        except Exception as e:
            logger.error(f"Error handling conversion amount: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _execute_coin_conversion(self, chat_id: int, message_id: int, from_currency: str, to_currency: str, amount: Decimal):
        """Execute coin conversion"""
        try:
            await self.edit_message(chat_id, message_id, f"🔄 **Conversie wordt uitgevoerd...**\n\n💱 {amount} {from_currency} → {to_currency}")

            # Execute conversion using advanced trading manager
            result = await self.advanced_trading.convert_coin(from_currency, to_currency, amount)

            if result['success']:
                success_message = f"""
✅ **Conversie Succesvol!**

💱 **Conversie:** {amount} {from_currency} → {to_currency}
📊 **Ontvangen:** {result.get('final_amount', 'N/A')} {to_currency}
🔄 **Route:** {' → '.join(result.get('conversion_path', []))}
📈 **Orders:** {len(result.get('orders', []))} uitgevoerd

**Conversie is voltooid!**
                """
            else:
                success_message = f"""
❌ **Conversie Mislukt**

💱 **Geprobeerd:** {amount} {from_currency} → {to_currency}
❌ **Fout:** {result.get('error', 'Onbekende fout')}

**Mogelijke oorzaken:**
• Onvoldoende saldo
• Handelspar niet beschikbaar
• Netwerk problemen

Probeer opnieuw of gebruik een ander bedrag.
                """

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("portfolio_convert"))

        except Exception as e:
            logger.error(f"Error executing conversion: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij conversie: {str(e)}", reply_markup=self.get_back_keyboard("portfolio_convert"))

    async def _handle_custom_futures_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle custom futures input"""
        try:
            parts = text.strip().upper().split()

            if len(parts) != 3:
                await self.send_message(chat_id, "❌ Ongeldig formaat. Gebruik: COIN AMOUNT LEVERAGE\nVoorbeeld: BTC 0.1 10")
                return

            coin, amount_str, leverage_str = parts

            try:
                if amount_str.lower() == 'all':
                    # Get all available balance
                    balances = await self.exchange_manager.get_all_balances()
                    total_balance = Decimal('0')
                    for exchange_balances in balances.values():
                        if coin in exchange_balances:
                            total_balance += exchange_balances[coin].free
                    amount = total_balance
                else:
                    amount = Decimal(amount_str)

                leverage = int(leverage_str)
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag of leverage. Gebruik getallen.")
                return

            if amount <= 0:
                await self.send_message(chat_id, "❌ Bedrag moet groter zijn dan 0")
                return

            if leverage < 1 or leverage > 100:
                await self.send_message(chat_id, "❌ Leverage moet tussen 1x en 100x zijn")
                return

            # Execute spot to futures conversion
            await self._execute_spot_to_futures(chat_id, original_message_id, coin, amount, leverage)

        except Exception as e:
            logger.error(f"Error handling custom futures: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _handle_futures_amount_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle futures amount input"""
        try:
            user_state = self.user_input_state.get(user_id, {})
            coin = user_state.get('coin')

            if not coin:
                await self.send_message(chat_id, "❌ Coin gegevens niet gevonden. Probeer opnieuw.")
                return

            try:
                if text.strip().lower() == 'all':
                    # Get all available balance
                    balances = await self.exchange_manager.get_all_balances()
                    total_balance = Decimal('0')
                    for exchange_balances in balances.values():
                        if coin in exchange_balances:
                            total_balance += exchange_balances[coin].free
                    amount = total_balance
                else:
                    amount = Decimal(text.strip())
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig bedrag. Gebruik een getal of 'all'")
                return

            if amount <= 0:
                await self.send_message(chat_id, "❌ Bedrag moet groter zijn dan 0")
                return

            # Ask for leverage
            leverage_message = f"""
⚡ **{coin} Spot→Futures - Stap 2**

**Bedrag:** {amount} {coin}

Welke leverage wil je gebruiken?

**Beschikbare Leverage:** 1x - 100x

**Aanbevelingen:**
• **Beginners:** 1x - 3x (veilig)
• **Ervaren:** 5x - 10x (gemiddeld risico)
• **Expert:** 20x+ (hoog risico)

**Let op:** Hoger leverage = hoger risico van liquidatie

Typ de leverage (bijv. 5 voor 5x leverage):
            """

            self.user_input_state[user_id] = {
                'expecting': 'futures_leverage',
                'coin': coin,
                'amount': amount,
                'chat_id': chat_id,
                'message_id': original_message_id
            }

            await self.edit_message(chat_id, original_message_id, leverage_message, reply_markup=self.get_back_keyboard("portfolio_futures"))

        except Exception as e:
            logger.error(f"Error handling futures amount: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def _execute_spot_to_futures(self, chat_id: int, message_id: int, coin: str, amount: Decimal, leverage: int = 1):
        """Execute spot to futures conversion"""
        try:
            await self.edit_message(chat_id, message_id, f"⚡ **Spot→Futures wordt uitgevoerd...**\n\n🪙 {amount} {coin}\n📊 Leverage: {leverage}x")

            # Execute conversion using advanced trading manager
            symbol = f"{coin}/USDT"
            result = await self.advanced_trading.convert_spot_to_futures(symbol, amount)

            if result['success']:
                success_message = f"""
✅ **Spot→Futures Succesvol!**

🪙 **Coin:** {coin}
💰 **Bedrag:** {amount}
📊 **Leverage:** {leverage}x
⚡ **Futures Positie:** Geopend

**Transfer Details:**
• Spot account → Futures account
• Positie grootte: {amount} {coin}
• Liquidatie prijs: Berekend op basis van leverage

**Futures positie is nu actief!**
                """
            else:
                success_message = f"""
❌ **Spot→Futures Mislukt**

🪙 **Geprobeerd:** {amount} {coin}
📊 **Leverage:** {leverage}x
❌ **Fout:** {result.get('error', 'Onbekende fout')}

**Mogelijke oorzaken:**
• Onvoldoende spot saldo
• Futures trading niet beschikbaar
• Leverage te hoog voor saldo

Probeer opnieuw met een lager bedrag of leverage.
                """

            await self.edit_message(chat_id, message_id, success_message, reply_markup=self.get_back_keyboard("portfolio_futures"))

        except Exception as e:
            logger.error(f"Error executing spot to futures: {e}")
            await self.edit_message(chat_id, message_id, f"❌ Fout bij spot→futures: {str(e)}", reply_markup=self.get_back_keyboard("portfolio_futures"))

    async def _handle_futures_leverage_input(self, chat_id: int, user_id: int, text: str, original_message_id: int):
        """Handle futures leverage input"""
        try:
            user_state = self.user_input_state.get(user_id, {})
            coin = user_state.get('coin')
            amount = user_state.get('amount')

            if not coin or not amount:
                await self.send_message(chat_id, "❌ Futures gegevens niet gevonden. Probeer opnieuw.")
                return

            try:
                leverage = int(text.strip())
            except ValueError:
                await self.send_message(chat_id, "❌ Ongeldig leverage. Gebruik een getal tussen 1 en 100.")
                return

            if leverage < 1 or leverage > 100:
                await self.send_message(chat_id, "❌ Leverage moet tussen 1x en 100x zijn")
                return

            # Execute spot to futures conversion
            await self._execute_spot_to_futures(chat_id, original_message_id, coin, amount, leverage)

        except Exception as e:
            logger.error(f"Error handling futures leverage: {e}")
            await self.send_message(chat_id, "❌ Er is een fout opgetreden. Probeer opnieuw.")

    async def initialize(self):
        """Initialize all components"""
        logger.info("🚀 Initializing Simple Telegram Bot...")

        # Validate settings
        if not self.settings.telegram_bot_token:
            logger.error("❌ Telegram bot token not configured")
            return False

        if not self.bot_token:
            logger.error("❌ No Telegram bot token found.")
            return False

        # Initialize HTTP session - OPTIMIZED FOR SPEED
        # Create SSL context with proper certificates
        ssl_context = ssl.create_default_context(cafile=certifi.where())

        # Optimize connector for faster responses
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=100,  # Connection pool size
            limit_per_host=30,  # Connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
            keepalive_timeout=30,  # Keep connections alive
            enable_cleanup_closed=True
        )

        # Optimize session with timeouts
        timeout = aiohttp.ClientTimeout(
            total=10,  # Total timeout
            connect=3,  # Connection timeout
            sock_read=5  # Socket read timeout
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'TradingBot/1.0'}
        )

        # Test bot token
        try:
            url = f"{self.base_url}/getMe"
            async with self.session.get(url) as response:
                result = await response.json()
                if result.get('ok'):
                    bot_info = result['result']
                    logger.info(f"✅ Bot connected: @{bot_info['username']}")
                else:
                    logger.error("❌ Invalid bot token")
                    return False
        except Exception as e:
            logger.error(f"❌ Error testing bot token: {e}")
            return False

        # Initialize exchange manager
        self.exchange_manager = ExchangeManager()

        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await self.exchange_manager.connect_all()

        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")

        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return False

        # Initialize strategy manager
        self.strategy_manager = StrategyManager(self.exchange_manager)

        # Initialize market analyzer
        self.market_analyzer = MarketAnalyzer(self.exchange_manager)

        logger.info("✅ All components initialized")
        return True

    async def run(self):
        """Run the bot"""
        if not await self.initialize():
            return

        self.running = True

        # Start with offset 0 to receive all new messages
        logger.info("🔍 Starting message polling from offset 0...")
        offset = 0
        logger.info("📍 Bot will process all new messages")

        # Start market analyzer in background (don't wait for it)
        asyncio.create_task(self.market_analyzer.start_analysis())

        logger.info("🚀 Telegram bot is running...")
        logger.info("Send /start to your bot to begin!")
        logger.info("📨 Bot is ready to receive messages!")

        try:
            logger.info("🔄 Starting message polling loop...")
            while self.running:
                # Get updates
                updates = await self.get_updates(offset)

                if updates and updates.get('ok'):
                    if updates['result']:  # Only log if there are updates
                        logger.info(f"📨 Received {len(updates['result'])} new updates")

                    for update in updates['result']:
                        try:
                            if 'message' in update:
                                logger.info(f"📩 Processing message from user {update['message']['from']['id']}: {update['message'].get('text', 'No text')}")
                                await self.handle_message(update['message'])
                            elif 'callback_query' in update:
                                logger.info(f"🔘 Processing callback from user {update['callback_query']['from']['id']}: {update['callback_query']['data']}")
                                await self.handle_callback_query(update['callback_query'])

                            # Update offset after successful processing
                            offset = update['update_id'] + 1

                        except Exception as e:
                            logger.error(f"❌ Error processing update {update.get('update_id', 'unknown')}: {e}")
                            # Still update offset to avoid getting stuck on bad updates
                            offset = update['update_id'] + 1

                elif updates:
                    logger.warning(f"❌ Telegram API error: {updates}")
                else:
                    logger.debug("🔍 No updates received, continuing...")

                await asyncio.sleep(1)  # Small delay

        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error in bot loop: {e}")
        finally:
            # Cleanup
            self.running = False
            self.market_analyzer.stop_analysis()
            self.strategy_manager.stop_automated_trading()

            if self.session:
                await self.session.close()

    async def _create_demo_trades(self, chat_id: int):
        """Create demo trades to show how trading works"""
        try:
            await asyncio.sleep(3)  # Wait a bit before showing demo trades

            demo_message = """
🎯 **Demo Trades Geplaatst!**

Hier zijn enkele voorbeeldtrades om te laten zien hoe de bot werkt:

📈 **BTC/USDT Long**
• Entry: $95,420.50
• Amount: 0.00105 BTC
• Stop-loss: $93,361.29 (-2.16%)
• Take-profit: $101,691.33 (+6.57%)
• Status: 🟢 Open

📉 **ETH/USDT Short**
• Entry: $3,642.80
• Amount: 0.0274 ETH
• Stop-loss: $3,721.26 (+2.15%)
• Take-profit: $3,423.03 (-6.03%)
• Status: 🟢 Open

⚡ **SOL/USDT Scalp**
• Entry: $242.15
• Amount: 0.412 SOL
• Stop-loss: $240.72 (-0.59%)
• Take-profit: $244.58 (+1.00%)
• Status: 🟡 Pending

💡 **Tip:** Gebruik 💰 Portfolio → Posities om je echte trades te bekijken!
            """

            await self.send_message(chat_id, demo_message)

        except Exception as e:
            logger.error(f"Error creating demo trades: {e}")

    async def _force_test_trades(self, chat_id: int):
        """Force some test trades to be executed"""
        try:
            await asyncio.sleep(5)  # Wait a bit after demo trades

            # Force a small BTC buy trade
            await self._force_buy_trade("BTC/USDT", 10.0, chat_id)

            await asyncio.sleep(2)

            # Force a small ETH buy trade
            await self._force_buy_trade("ETH/USDT", 15.0, chat_id)

        except Exception as e:
            logger.error(f"Error forcing test trades: {e}")

    async def _force_buy_trade(self, symbol: str, amount_usd: float, chat_id: int):
        """Force execute a buy trade"""
        try:
            # Get current price
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                logger.error(f"No ticker data for {symbol}")
                return

            # Use best ticker
            best_ticker = max(tickers.values(), key=lambda t: t.volume)
            current_price = float(best_ticker.last)

            # Calculate amount to buy
            crypto_amount = amount_usd / current_price

            # Find best exchange
            best_exchange_result = await self.exchange_manager.find_best_price(symbol, "buy")
            if not best_exchange_result:
                logger.error(f"No exchange available for {symbol}")
                return

            best_exchange, best_price = best_exchange_result

            # Try to create real order
            try:
                order = await self.exchange_manager.create_order(
                    exchange_name=best_exchange,
                    order_type="market",
                    side="buy",
                    symbol=symbol,
                    amount=crypto_amount
                )

                if order:
                    success_msg = f"""
🎯 **Echte Trade Uitgevoerd!**

💰 **Bedrag:** ${amount_usd}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **Amount:** {crypto_amount:.8f}
💵 **Prijs:** ${current_price:.2f}
🆔 **Order ID:** {order.id}
📈 **Status:** {order.status}

✅ **Dit is een echte trade!**
                    """
                else:
                    success_msg = f"""
⚠️ **Test Trade Gesimuleerd**

💰 **Bedrag:** ${amount_usd}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **Amount:** {crypto_amount:.8f}
💵 **Prijs:** ${current_price:.2f}

📝 **Status:** Simulatie (geen echte order geplaatst)
                    """

            except Exception as order_error:
                logger.error(f"Order creation failed: {order_error}")
                success_msg = f"""
⚠️ **Trade Poging**

💰 **Bedrag:** ${amount_usd}
📊 **Exchange:** {best_exchange.upper()}
🎯 **Symbol:** {symbol}
💎 **Amount:** {crypto_amount:.8f}
💵 **Prijs:** ${current_price:.2f}

❌ **Error:** {str(order_error)[:100]}...
                """

            await self.send_message(chat_id, success_msg)

        except Exception as e:
            logger.error(f"Error in force buy trade: {e}")


async def main():
    """Main function"""
    bot = SimpleTelegramBot()
    await bot.run()

if __name__ == "__main__":
    asyncio.run(main())
