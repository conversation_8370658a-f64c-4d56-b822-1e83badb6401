#!/bin/bash
# 🚀 Trading Bot Startup Script voor Myownmoneymaker

echo "🤖 Trading Bot Startup"
echo "======================"

# Check if Python is beschikbaar
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 niet gevonden. Installeer Python 3.8 of hoger."
    exit 1
fi

# Check of script in de juiste folder zit
if [ ! -f "main.py" ] && [ ! -f "config/settings.py" ]; then
    echo "❌ Niet in juiste directory. Draai dit vanuit de hoofdmap van je project."
    exit 1
fi

# Activeer virtuele omgeving als aanwezig
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
elif [ -f "venv/Scripts/activate" ]; then
    source venv/Scripts/activate
fi

# Installeer dependencies
echo "📦 Dependencies installeren..."
python3 -m pip install --upgrade pip
python3 -m pip install -r requirements.txt

# Check op .env
if [ ! -f ".env" ]; then
    echo "❌ .env niet gevonden!"
    echo "Voer het volgende uit om er één te maken:"
    echo "  python3 scripts/env_generator.py"
    exit 1
fi

echo "✅ Setup compleet!"
echo ""

# Keuzemenu
echo "🎯 Kies het type bot dat je wil starten:"
echo "1. Console Bot (Terminal UI)"
echo "2. Telegram Bot (Mobiele interface)"
echo "3. Test exchanges connecties"

read -p "Maak je keuze (1, 2 of 3): " keuze

case $keuze in
    1)
        echo "🚀 Start Console Bot..."
        python3 main.py
        ;;
    2)
        echo "🚀 Start Telegram Bot..."
        python3 bot/telegram_router.py
        ;;
    3)
        echo "🧪 Test connectie met exchanges..."
        python3 tests/test_exchanges.py
        ;;
    *)
        echo "❌ Ongeldige keuze. Start standaard Console Bot..."
        python3 main.py
        ;;
esac
