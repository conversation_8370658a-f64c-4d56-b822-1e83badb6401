{"description": "Verbeter het risk management systeem van deze trading bot. Focus op realistische checks zoals drawdown en daily loss, en maak koppelingen met exchange balances via config en exchange modules.", "goals": ["Voeg implementatie toe voor _check_portfolio_exposure()", "Gebruik exchange balansen via exchanges/kucoin.py of andere modules", "Implementeer _check_drawdown() met berekening op basis van gerealiseerd verlies", "Laat _check_daily_loss_limit() gebruikmaken van session-based loss tracking", "Lees risicoparameters uit config/settings.py i.p.v. hardcoded"], "files": ["core/risk_manager.py", "config/settings.py", "exchanges/kucoin.py"], "tools": ["python", "file_system"]}