import os
import subprocess
from pathlib import Path

def check_git_repo():
    try:
        top_level = subprocess.check_output(["git", "rev-parse", "--show-toplevel"]).decode().strip()
        return Path(top_level)
    except subprocess.CalledProcessError:
        print("❌ Dit is geen geldige Git repository.")
        return None

def check_hook_exists(repo_path):
    hook_path = repo_path / ".git" / "hooks" / "pre-commit"
    if not hook_path.exists():
        print(f"❌ pre-commit hook bestaat niet: {hook_path}")
        return None
    print(f"✅ pre-commit hook gevonden: {hook_path}")
    return hook_path

def make_hook_executable(hook_path):
    if not os.access(hook_path, os.X_OK):
        print("🛠️  pre-commit hook is nog NIET uitvoerbaar, pas dat aan...")
        subprocess.run(["chmod", "+x", str(hook_path)])
        print("✅ pre-commit hook is nu uitvoerbaar.")
    else:
        print("✅ pre-commit hook is al uitvoerbaar.")

def test_hook(hook_path):
    print("🧪 Hook test uitvoeren...")
    result = subprocess.run([str(hook_path)], capture_output=True, text=True)
    print("--- HOOK OUTPUT ---")
    print(result.stdout or result.stderr)

if __name__ == "__main__":
    repo_path = check_git_repo()
    if not repo_path:
        exit(1)

    hook = check_hook_exists(repo_path)
    if not hook:
        exit(1)

    make_hook_executable(hook)
    test_hook(hook)
